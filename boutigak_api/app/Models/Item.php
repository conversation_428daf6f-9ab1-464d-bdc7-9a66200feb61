<?php

namespace App\Models;

use App\Models\Media;
use App\Models\Order;
use App\Models\ItemMedia;
use App\Enums\eItemStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Item extends Model
{
    protected $with = ['images' , 'user' , 'categoryDetails'];

    protected $table = 'item';

    protected $fillable = [
        'is_promoted',
        'title',
        'title_ar',
        'description',
        'description_ar',
        'price',
        'sold_out',
        'condition',
        'status',
        'quantity',
        'brand_id',
        'user_id',
        'store_id',
        'category_id',
        'rejection_reason',
        'promotion_percentage',
        'has_promotion',
        'matterport_link'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    protected function price(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if ($this->has_promotion && $this->promotion_percentage > 0) {
                    $discount = ($value * $this->promotion_percentage) / 100;
                    return $value - $discount;
                }
                return $value;
            }
        );
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function categoryDetails()
    {
        return $this->hasMany(CategoryItemDetail::class, 'item_id');
    }

    public function likedByUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'liked_item');
    }



    public function images()
    {
        return $this->hasManyThrough(
            Media::class,
            ItemMedia::class,
            'item_id',
            'id',
            'id',
            'media_id'
        )
        ->select(['media.*', 'item_media.id as item_media_id', 'item_media.order'])
        ->orderBy('item_media.order', 'asc'); 
    }

    public function orders()
    {
        return $this->belongsToMany(Order::class, 'order_item')
                    ->withPivot('quantity', 'price')
                    ->withTimestamps();
    }



    
    public function scopeSearch(Builder $query, string $searchTerm)
{
    $userId = auth()->user()->id ?? null;

    $searchTerm = trim($searchTerm);

    return $query->where(function (Builder $query) use ($searchTerm) {
        $query->where(DB::raw('LOWER(title)'), 'like', '%' . strtolower($searchTerm) . '%')
              ->orWhere(DB::raw('LOWER(title_ar)'), 'like', '%' . strtolower($searchTerm) . '%') // Add this line for Item's title_ar
              ->orWhere(DB::raw('LOWER(description)'), 'like', '%' . strtolower($searchTerm) . '%')
              ->orWhere(DB::raw('LOWER(description_ar)'), 'like', '%' . strtolower($searchTerm) . '%') // Add this line for Item's description_ar
              ->orWhereHas('category', function (Builder $query) use ($searchTerm) {
                  $query->where(DB::raw('LOWER(title_en)'), 'like', '%' . strtolower($searchTerm) . '%')
                        ->orWhere(DB::raw('LOWER(title_ar)'), 'like', '%' . strtolower($searchTerm) . '%')
                        ->orWhere(DB::raw('LOWER(title_fr)'), 'like', '%' . strtolower($searchTerm) . '%');
              })
              ->orWhereHas('brand', function (Builder $query) use ($searchTerm) {
                  $query->where(DB::raw('LOWER(name)'), 'like', '%' . strtolower($searchTerm) . '%');
              });
    })->where(function ($query) use ($userId) {
        $query->where('user_id', '!=', $userId)
              ->orWhereNull('user_id');
    })->where('status', eItemStatus::APPROVED);
}
    /**
     * Get the followers for the item.
     */
    public function followers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'liked_item');
    }

    public function histories()
    {
        return $this->hasMany(ItemHistory::class);
    }
    
    public function payments(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ItemPayment::class);
    }

    /**
     * Get the ordered images for the item.
     *
     * @return array
     */
    public function getOrderedImagesAttribute()
    {
        $transformedImages = [];
        
        foreach ($this->images as $image) {
            $order = ItemMedia::where('item_id', $this->id)
                              ->where('media_id', $image->item_media_id)
                              ->value('order') ?? 0;
            
            $transformedImages[] = [
                'id' => $image->item_media_id,
                'url' => $image->url,
                'order' => $order,
            ];
        }
        
        // Sort the transformed images by order
        usort($transformedImages, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        
        return $transformedImages;
    }

    protected static function booted()
    {
        static::addGlobalScope('user_not_deleted', function ($query) {
            $query->where(function($q) {
                $q->whereHas('user', function($userQuery) {
                    $userQuery->where('is_deleted', false);
                })->orWhereDoesntHave('user');
            });
        });
    }
}

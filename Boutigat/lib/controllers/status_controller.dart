
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/services/brands_service.dart';
import 'package:boutigak/data/services/status_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/status.dart';



class StatusController extends GetxController {
  final AuthController authController = Get.find<AuthController>();
  Rx<Status?> selectedItem = Rx<Status?>(null);
  RxString id;
  RxString title = ''.obs;
  RxString status = ''.obs;
  RxString image = ''.obs;
  RxList<Status> myItems = <Status>[].obs;

  // Pagination variables
  var currentPage = 1.obs;
  var hasMoreItems = true.obs;
  var isLoading = false.obs;
  var isLoadingMore = false.obs;
  final int perPage = 10;

  // Modify later to required
  StatusController({
    String initialId = '',
    String initialTitle = '',
    String initialImage= '',
    String initialStatus = '',
  }) : id = (initialId.isNotEmpty ? initialId : '').obs {
    title.value = initialTitle;
    status.value = initialStatus;
    image.value = initialImage;
  }

 
Future<void> fetchMyItems({bool refresh = false}) async {
    if (refresh && isLoading.value) return;
    if (!refresh && isLoadingMore.value) return;

    if (refresh) {
      currentPage.value = 1;
      myItems.clear();
      hasMoreItems.value = true;
      isLoading.value = true;
    } else {
      // Loading more items
      if (!hasMoreItems.value) {
        print('No more items to load');
        return;
      }
      isLoadingMore.value = true;
    }

    print('Fetching my items for page: ${currentPage.value}');

    try {
      List<Status>? fetchedMyItems;

      // Vérifie si l'utilisateur est authentifié
      if (authController.isAuthenticated.value) {
        // Si l'utilisateur est connecté, fetchItems
        fetchedMyItems = await StatusService.fetchMyItems(
          page: currentPage.value,
          perPage: perPage,
        );
      }

      // Si les éléments sont récupérés correctement, mettre à jour la liste
      if (fetchedMyItems != null) {
        if (fetchedMyItems.isEmpty) {
          print('No more items received from server');
          hasMoreItems.value = false;
        } else {
          print('Received ${fetchedMyItems.length} new items');
          for (var newItem in fetchedMyItems) {
            if (!myItems.any((existingItem) => existingItem.id == newItem.id)) {
              myItems.add(newItem);
            }
          }
          currentPage.value++;
        }
      }
    } catch (e) {
      print('Error fetching my items: $e');
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  
  // Method to select an item and make it reactive
  void selectItem(Status status) {
    selectedItem.value = status;  // Set the current item to be observed
  }

  

  @override
  void onInit() {
    super.onInit();
    fetchMyItems();
  }
}
